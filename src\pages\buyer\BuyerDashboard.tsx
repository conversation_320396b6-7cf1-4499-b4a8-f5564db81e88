import React, { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import { useAuth } from '../../contexts/AuthContext'
import Layout from '../../components/Layout'

interface Product {
  id: number
  product_name: string
  quantity: number
  price: number
  seller_name?: string
}

const BuyerDashboard: React.FC = () => {
  const { user, logout } = useAuth()
  const [products, setProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')

  useEffect(() => {
    fetchProducts()
  }, [])

  const fetchProducts = async () => {
    try {
      setLoading(true)
      // TODO: Replace with actual API call
      // const response = await buyerService.getProducts()
      // setProducts(response.data)
      
      // Mock data for now
      setProducts([
        { id: 1, product_name: 'Gas Cylinder 12kg', quantity: 50, price: 2500 },
        { id: 2, product_name: 'Gas Cylinder 6kg', quantity: 30, price: 1500 },
        { id: 3, product_name: 'Gas Cylinder 25kg', quantity: 20, price: 4500 },
      ])
    } catch (err: any) {
      setError(err.message || 'Failed to fetch products')
    } finally {
      setLoading(false)
    }
  }

  return (
    <Layout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Buyer Dashboard</h1>
          <p className="mt-2 text-gray-600">Welcome back, {user?.email}</p>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Link
            to="/buyer/order"
            className="bg-blue-600 text-white p-6 rounded-lg hover:bg-blue-700 transition-colors"
          >
            <h3 className="text-lg font-semibold">Place New Order</h3>
            <p className="mt-2 text-blue-100">Order gas cylinders for delivery</p>
          </Link>
          
          <Link
            to="/buyer/orders"
            className="bg-green-600 text-white p-6 rounded-lg hover:bg-green-700 transition-colors"
          >
            <h3 className="text-lg font-semibold">My Orders</h3>
            <p className="mt-2 text-green-100">View your order history</p>
          </Link>
          
          <button
            onClick={logout}
            className="bg-red-600 text-white p-6 rounded-lg hover:bg-red-700 transition-colors text-left"
          >
            <h3 className="text-lg font-semibold">Logout</h3>
            <p className="mt-2 text-red-100">Sign out of your account</p>
          </button>
        </div>

        {/* Available Products */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900">Available Products</h2>
          </div>
          
          {loading ? (
            <div className="p-6 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-2 text-gray-600">Loading products...</p>
            </div>
          ) : error ? (
            <div className="p-6 text-center text-red-600">
              <p>{error}</p>
              <button
                onClick={fetchProducts}
                className="mt-2 text-blue-600 hover:text-blue-800"
              >
                Try again
              </button>
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {products.map((product) => (
                <div key={product.id} className="p-6 flex justify-between items-center">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900">{product.product_name}</h3>
                    <p className="text-gray-600">Available: {product.quantity} units</p>
                    <p className="text-lg font-semibold text-green-600">RWF {product.price.toLocaleString()}</p>
                  </div>
                  <Link
                    to={`/buyer/order?product=${product.id}`}
                    className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors"
                  >
                    Order Now
                  </Link>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </Layout>
  )
}

export default BuyerDashboard
