import React from 'react'
import { Link } from 'react-router-dom'
import { useAuth } from '../contexts/AuthContext'

interface LayoutProps {
  children: React.ReactNode
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const { user, logout } = useAuth()

  const getNavigationLinks = () => {
    if (!user) return []

    switch (user.role) {
      case 'buyer':
        return [
          { name: 'Dashboard', href: '/buyer/dashboard' },
          { name: 'Place Order', href: '/buyer/order' },
          { name: 'My Orders', href: '/buyer/orders' },
        ]
      case 'seller':
        return [
          { name: 'Dashboard', href: '/seller/dashboard' },
          { name: 'Inventory', href: '/seller/inventory' },
          { name: 'Orders', href: '/seller/orders' },
        ]
      case 'admin':
        return [
          { name: 'Dashboard', href: '/admin/dashboard' },
          { name: 'Orders', href: '/admin/orders' },
          { name: 'Invoices', href: '/admin/invoices' },
          { name: 'Users', href: '/admin/users' },
          { name: 'Reports', href: '/admin/reports' },
        ]
      default:
        return []
    }
  }

  const navigationLinks = getNavigationLinks()

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation */}
      <nav className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex">
              <div className="flex-shrink-0 flex items-center">
                <Link to="/" className="text-xl font-bold text-gray-900">
                  GasGo Rwanda
                </Link>
              </div>
              <div className="hidden sm:ml-6 sm:flex sm:space-x-8">
                {navigationLinks.map((link) => (
                  <Link
                    key={link.name}
                    to={link.href}
                    className="text-gray-900 hover:text-blue-600 inline-flex items-center px-1 pt-1 text-sm font-medium"
                  >
                    {link.name}
                  </Link>
                ))}
              </div>
            </div>
            <div className="flex items-center space-x-4">
              {user && (
                <>
                  <span className="text-sm text-gray-700">
                    {user.email} ({user.role})
                  </span>
                  <button
                    onClick={logout}
                    className="bg-red-600 text-white px-3 py-2 rounded text-sm hover:bg-red-700 transition-colors"
                  >
                    Logout
                  </button>
                </>
              )}
            </div>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main>{children}</main>
    </div>
  )
}

export default Layout
