import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { authService } from '../services/authService'

interface User {
  id: number
  email: string
  role: 'buyer' | 'seller' | 'admin'
}

interface AuthContextType {
  user: User | null
  token: string | null
  login: (email: string, password: string) => Promise<void>
  register: (email: string, password: string, confirmPassword: string, role: 'buyer' | 'seller') => Promise<void>
  logout: () => void
  loading: boolean
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

interface AuthProviderProps {
  children: ReactNode
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null)
  const [token, setToken] = useState<string | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Check for existing token on app load
    const storedToken = localStorage.getItem('token')
    if (storedToken) {
      setToken(storedToken)
      // You might want to validate the token with the backend here
      // For now, we'll decode the user info from the token or make an API call
      // This is a simplified version - in production, you'd want to verify the token
      try {
        const payload = JSON.parse(atob(storedToken.split('.')[1]))
        setUser({
          id: payload.user_id,
          email: payload.email,
          role: payload.role
        })
      } catch (error) {
        // Invalid token, remove it
        localStorage.removeItem('token')
      }
    }
    setLoading(false)
  }, [])

  const login = async (email: string, password: string) => {
    try {
      const response = await authService.login(email, password)
      const { access } = response.data
      
      setToken(access)
      localStorage.setItem('token', access)
      
      // Decode user info from token
      const payload = JSON.parse(atob(access.split('.')[1]))
      setUser({
        id: payload.user_id,
        email: payload.email,
        role: payload.role
      })
    } catch (error) {
      throw error
    }
  }

  const register = async (email: string, password: string, confirmPassword: string, role: 'buyer' | 'seller') => {
    try {
      await authService.register(email, password, confirmPassword, role)
      // After successful registration, you might want to auto-login
      await login(email, password)
    } catch (error) {
      throw error
    }
  }

  const logout = async () => {
    try {
      if (token) {
        await authService.logout()
      }
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      setUser(null)
      setToken(null)
      localStorage.removeItem('token')
    }
  }

  const value = {
    user,
    token,
    login,
    register,
    logout,
    loading
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}
